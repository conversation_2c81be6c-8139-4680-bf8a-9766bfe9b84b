 

import {
  createGeneralDetails,
  createPatientDetails,
  createPractitionerDetails,
  createOrganizationDetails,
  createEncounterDetails,
  createBinaryDetails,
  createSignatureDetails
} from './fhir.data.js';

import { fetchImageAsBase64 } from "./common.utils.js";

/**
 * Creates ABDM-compliant MedicationRequest resource (REQUIRED for PrescriptionRecord)
 * @param {Object} medication - Medication details
 * @param {string} patientReference - Patient resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @returns {Object} FHIR MedicationRequest resource
 */
export const createABDMMedicationRequestResource = (
  medication,
  patientReference,
  encounterReference,
  practitionerReference
) => ({
  resourceType: "MedicationRequest",
  id: `medication-request-${medication._id || Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest"]
  },
  identifier: [
    {
      use: "usual",
      system: "https://www.healtether.com/medication-request",
      value: medication._id?.toString() || `med-req-${Date.now()}`
    }
  ],
  status: "active",
  intent: "order",
  category: [
    {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/medicationrequest-category",
          code: "outpatient",
          display: "Outpatient"
        }
      ]
    }
  ],
  medicationCodeableConcept: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: medication.snomedCode || "387517004",
        display: medication.drugName || "Medication"
      }
    ],
    text: medication.drugName || "Medication"
  },
  subject: {
    reference: patientReference
  },
  encounter: {
    reference: encounterReference
  },
  authoredOn: new Date().toISOString(),
  requester: {
    reference: practitionerReference
  },
  dosageInstruction: [
    {
      text: medication.notes || medication.content || "As directed",
      additionalInstruction: medication.additionalInstructions ? [
        {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "311504000",
              display: "With or after food"
            }
          ],
          text: medication.additionalInstructions
        }
      ] : undefined,
      timing: {
        repeat: {
          frequency: medication.frequency || 1,
          period: medication.duration?.value || 1,
          periodUnit: medication.duration?.unit || "d"
        }
      },
      route: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "26643006",
            display: "Oral route"
          }
        ]
      },
      method: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "421521009",
            display: "Swallow"
          }
        ]
      },
      doseAndRate: [
        {
          doseQuantity: {
            value: medication.dosage || 1,
            unit: "tablet",
            system: "http://unitsofmeasure.org",
            code: "1"
          }
        }
      ]
    }
  ],
  note: medication.content ? [
    {
      text: medication.content
    }
  ] : undefined
});

/**
 * Creates ABDM-compliant Composition resource for PrescriptionRecord following NDHM specification
 * @param {string} patientReference - Patient resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @param {string} organizationReference - Organization resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {Array} medicationRequestReferences - Array of MedicationRequest resource references
 * @param {string} binaryReference - Binary resource reference for prescription PDF
 * @returns {Object} FHIR Composition resource compliant with ABDM PrescriptionRecord
 */
export const createABDMPrescriptionCompositionResource = (
  patientReference,
  practitionerReference,
  organizationReference,
  encounterReference,
  medicationRequestReferences,
  binaryReference
) => ({
  resourceType: "Composition",
  id: `composition-prescription-${Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/PrescriptionRecord"]
  },
  identifier: {
    system: "https://www.healtether.com/prescription-composition",
    value: `prescription-comp-${Date.now()}`
  },
  status: "final",
  // ABDM Required: Fixed SNOMED code for PrescriptionRecord
  type: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: "440545006",
        display: "Prescription record"
      }
    ],
    text: "Prescription record"
  },
  subject: {
    reference: patientReference
  },
  encounter: {
    reference: encounterReference
  },
  date: new Date().toISOString(),
  author: [
    {
      reference: practitionerReference
    }
  ],
  title: "Prescription record",
  custodian: {
    reference: organizationReference
  },
  // ABDM Required: Single section with MedicationRequest and Binary entries
  section: [
    {
      title: "Prescription record",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "440545006",
            display: "Prescription record"
          }
        ],
        text: "Prescription record"
      },
      entry: [
        // MedicationRequest entries (REQUIRED)
        ...medicationRequestReferences.map(ref => ({
          reference: ref,
          type: "MedicationRequest"
        })),
        // Binary entry (OPTIONAL)
        ...(binaryReference ? [{
          reference: binaryReference,
          type: "Binary"
        }] : [])
      ]
    }
  ]
});

/**
 * Creates ABDM-compliant DocumentBundle for PrescriptionRecord
 * @param {Object} compositionResource - FHIR Composition resource
 * @param {Object} patientResource - FHIR Patient resource
 * @param {Object} practitionerResource - FHIR Practitioner resource
 * @param {Object} organizationResource - FHIR Organization resource
 * @param {Object} encounterResource - FHIR Encounter resource
 * @param {Array} medicationRequestResources - Array of FHIR MedicationRequest resources
 * @param {Object} binaryResource - FHIR Binary resource
 * @returns {Object} ABDM-compliant DocumentBundle
 */
export const createABDMPrescriptionDocumentBundle = (
  compositionResource,
  patientResource,
  practitionerResource,
  organizationResource,
  encounterResource,
  medicationRequestResources,
  binaryResource
) => ({
  resourceType: "Bundle",
  id: `prescription-bundle-${Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle"]
  },
  identifier: {
    system: "https://www.healtether.com/bundle",
    value: `prescription-bundle-${Date.now()}`
  },
  type: "document",
  timestamp: new Date().toISOString(),
  entry: [
    // 1. Composition (must be first entry in document bundle)
    {
      fullUrl: `Composition/${compositionResource.id}`,
      resource: compositionResource
    },
    // 2. Patient
    {
      fullUrl: `Patient/${patientResource.id}`,
      resource: patientResource
    },
    // 3. Practitioner
    {
      fullUrl: `Practitioner/${practitionerResource.id}`,
      resource: practitionerResource
    },
    // 4. Organization
    {
      fullUrl: `Organization/${organizationResource.id}`,
      resource: organizationResource
    },
    // 5. Encounter
    {
      fullUrl: `Encounter/${encounterResource.id}`,
      resource: encounterResource
    },
    // 6. MedicationRequests
    ...medicationRequestResources.map(medReq => ({
      fullUrl: `MedicationRequest/${medReq.id}`,
      resource: medReq
    })),
    // 7. Binary (if present)
    ...(binaryResource ? [{
      fullUrl: `Binary/${binaryResource.id}`,
      resource: binaryResource
    }] : [])
  ]
});

/**
 * Creates ABDM-compliant PrescriptionRecord structure for NDHM FHIR R4 compliance
 * @param {string} artifact - Artifact type (e.g., "PrescriptionRecord")
 * @param {Object} clinicData - Clinic/organization data
 * @param {Object} patient - Patient data with ABHA information
 * @param {Object} appointmentData - Appointment/encounter data
 * @param {Object} practitionerData - Practitioner/doctor data
 * @param {Object} prescription - Prescription data with medications
 * @param {Object} medicalHistory - Patient medical history
 * @returns {Object} ABDM-compliant PrescriptionRecord structure
 */
export const createABDMCompliantPrescriptionStructure = async (
  artifact,
  clinicData,
  patient,
  appointmentData,
  practitionerData,
  prescription,
  medicalHistory
) => {
  try {
    // Get PDF data
    const base64Data = await fetchImageAsBase64(
      appointmentData.prescriptionReport[0].blobName,
      appointmentData?.clinic
    );

    // Create resource references following FHIR standards
    const patientReference = `Patient/${patient._id}`;
    const practitionerReference = `Practitioner/${practitionerData._id}`;
    const organizationReference = `Organization/${clinicData._id}`;
    const encounterReference = `Encounter/${appointmentData._id}`;
    const binaryReference = `Binary/prescription-pdf-${appointmentData._id}`;

    // Create ABDM-compliant MedicationRequest resources (REQUIRED for PrescriptionRecord)
    const medicationRequestResources = prescription.prescriptions?.drugPrescriptions?.map(medication =>
      createABDMMedicationRequestResource(
        medication,
        patientReference,
        encounterReference,
        practitionerReference
      )
    ) || [];

    const medicationRequestReferences = medicationRequestResources.map(resource => `MedicationRequest/${resource.id}`);

    // Create FHIR resources
    const patientResource = createPatientDetails(
      patient._id,
      patient.abhaNumber,
      patient.abhaAddress,
      {
        text: `${patient.firstName + patient.lastName}`,
        prefix: [`${patient.prefix}`],
      },
      patient.gender,
      patient.birthday,
      patient?.address,
      [`${practitionerData.firstName + practitionerData.lastName}`],
      medicalHistory.allergies || [],
      patient.mobile
    );

    const practitionerResource = createPractitionerDetails(practitionerData);
    const organizationResource = createOrganizationDetails(clinicData);
    const encounterResource = createEncounterDetails(appointmentData);
    const binaryResource = createBinaryDetails("application/pdf", base64Data);

    // Create ABDM-compliant Composition resource
    const compositionResource = createABDMPrescriptionCompositionResource(
      patientReference,
      practitionerReference,
      organizationReference,
      encounterReference,
      medicationRequestReferences,
      binaryReference
    );

    // Create ABDM DocumentBundle
    const documentBundle = createABDMPrescriptionDocumentBundle(
      compositionResource,
      patientResource,
      practitionerResource,
      organizationResource,
      encounterResource,
      medicationRequestResources,
      binaryResource
    );

    // Return ABDM-compliant structure for Communications service
    return {
      // ABDM DocumentBundle (primary structure)
      bundle: documentBundle,

      // Legacy structure for backward compatibility
      general: createGeneralDetails(
        artifact,
        "https://www.healtether.com",
        ["hip1", "hip2"],
        "final",
        "SBX_003515"
      ),
      patient: patientResource,
      practitioners: [practitionerResource],
      organization: organizationResource,
      encounter: encounterResource,
      composition: compositionResource,
      medicationRequests: medicationRequestResources,
      binary: binaryResource,
      signature: createSignatureDetails("", [
        `${practitionerData.firstName + practitionerData.lastName}`,
      ]),
    };
  } catch (error) {
    console.error("Error creating ABDM-compliant Prescription structure:", error);
    throw new Error(`ABDM Prescription Creation Failed: ${error.message}`);
  }
};
