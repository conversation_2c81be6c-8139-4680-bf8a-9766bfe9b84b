import { Schema } from "mongoose";

const telecomSchema = new Schema({
  system: String,
  value: String,
  use: String,
});

const licenseSchema = new Schema({
  code: String,
  display: String,
  licNo: String,
});

const signatureSchema = new Schema({
  who: {
    type: { type: String },
    name: String,
  },
  sigFormat: String,
  data: String,
});

const addressSchema = new Schema({
  use: String,
  type: String,
  text: String,
  // line: [String],
  city: String,
  state: String,
  district: String,
  postalCode: String,
  country: String,
});

const allergyIntoleranceSchema = new Schema({
  type: String,
  clinicalStatus: String,
  verificationStatus: String,
  doctor: String,
  notes: [String],
});

const patientSchema = new Schema({
  id: String,
  abhaNumber: String,
  abhaAddress: String,
  name: {
    text: String,
    prefix: [String],
  },
  gender: String,
  dob: String,
  doctors: [String],
  allergyIntolerances: [allergyIntoleranceSchema],
  telecom: [telecomSchema],
  address: [addressSchema],
});

const generalSchema = new Schema({
  artifact: String,
  hipUrl: String,
  hipIds: [String],
  status: String,
  clientId: String,
});

const practitionerSchema = new Schema({
  names: [String],
  licenses: [licenseSchema],
  patient: String,
  gender: String,
  birthDate: String,
  address: [addressSchema],
  telecom: [telecomSchema],
});

const encounterSchema = new Schema({
  status: String,
  startTime: String,
  endTime: String,
});

const organizationSchema = new Schema({
  name: String,
  telecom: [telecomSchema],
  licenses: [licenseSchema],
});

// ABDM-compliant FHIR Quantity schema
const quantitySchema = new Schema(
  {
    value: { type: Number, required: true },
    unit: { type: String },
    system: { type: String },
    code: { type: String }
  },
  { _id: false }
);

// Sub-schema for amount (moved up to avoid reference errors)
const amountSchema = new Schema(
  {
    value: { type: Number, required: true },
    currency: { type: String, required: true },
  },
  { _id: false }
);

// ABDM-compliant coding schema
const codingSchema = new Schema(
  {
    system: { type: String },
    code: { type: String },
    display: { type: String }
  },
  { _id: false }
);

// ABDM-compliant CodeableConcept schema
const codeableConceptSchema = new Schema(
  {
    coding: [codingSchema],
    text: { type: String }
  },
  { _id: false }
);

// ABDM-compliant Reference schema
const referenceSchema = new Schema(
  {
    reference: { type: String },
    display: { type: String }
  },
  { _id: false }
);

// ABDM-compliant Performer schema for ChargeItem
const performerSchema = new Schema(
  {
    function: codeableConceptSchema,
    actor: referenceSchema
  },
  { _id: false }
);

// ABDM-compliant ChargeItem schema
const chargeItemSchema = new Schema({
  resourceType: { type: String, default: "ChargeItem" },
  id: String,
  meta: {
    versionId: String,
    lastUpdated: String,
    profile: [String]
  },
  identifier: [{
    use: String,
    system: String,
    value: String
  }],
  status: String,
  code: codeableConceptSchema,
  subject: referenceSchema,
  context: referenceSchema,
  occurrenceDateTime: String,
  performer: [performerSchema],
  quantity: quantitySchema, // Now supports FHIR Quantity object
  priceOverride: amountSchema,
  overrideReason: String,
  enterer: referenceSchema,
  enteredDate: String
});

// const invoiceLineItemSchema = new Schema({
//     priceComponent: [{
//         type: String,
//         display:String,
//         amount: {
//             type: Number,
//             currency: String
//         },
//     }]
// });



// ABDM-compliant priceComponent schema
const priceComponentSchema = new Schema(
  {
    type: { type: String, required: true },
    code: codeableConceptSchema, // ABDM price component codes
    amount: amountSchema,
  },
  { _id: false }
);

// ABDM-compliant lineItem schema for Invoice
const invoiceLineItemSchema = new Schema(
  {
    sequence: { type: Number }, // ABDM standard sequence number
    chargeItemReference: referenceSchema, // Reference to ChargeItem
    priceComponent: [priceComponentSchema],
  },
  { _id: false }
);

// ABDM-compliant Invoice schema
const invoiceSchema = new Schema({
  resourceType: { type: String, default: "Invoice" },
  id: { type: String, required: true },
  meta: {
    versionId: String,
    lastUpdated: String,
    profile: [String]
  },
  identifier: [{
    value: String
  }],
  status: { type: String, required: true },
  type: codeableConceptSchema, // ABDM billing type codes
  subject: referenceSchema, // Patient reference
  date: { type: String, required: true },
  participant: [{
    actor: referenceSchema // Practitioner reference
  }],
  lineItem: [invoiceLineItemSchema],
  totalNet: amountSchema, // Amount after taxes (ABDM standard)
  totalGross: amountSchema, // Amount before taxes (ABDM standard)
});

const attachmentSchema = new Schema({
  contentType: String,
  language: String,
  data: String,
  title: String,
  creation: String,
});

const documentReferenceSchema = new Schema({
  status: String,
  docStatus: String,
  type: String,
  content: [{ attachment: attachmentSchema }],
});

// ABDM-compliant Binary schema
const binarySchema = new Schema({
  resourceType: { type: String, default: "Binary" },
  id: String,
  meta: {
    versionId: String,
    lastUpdated: String,
    profile: [String]
  },
  contentType: String,
  data: String,
});

const InvoiceRecordSchema = new Schema({
  fhirId: {
    type: String,
    required: true,
    index: true,
  },
  general: generalSchema,
  patient: patientSchema,
  practitioners: [practitionerSchema],
  encounter: encounterSchema,
  organization: organizationSchema,
  invoice: invoiceSchema,
  binary: binarySchema,
  chargeItems: [chargeItemSchema],
  signature: signatureSchema,

  abhaCareContextLinked: {
    type: Boolean,
    default: false,
  },
});

export { InvoiceRecordSchema };
