 

import {
  createGeneralDetails,
  createPatientDetails,
  createPractitionerDetails,
  createOrganizationDetails,
  createEncounterDetails,
  createSignatureDetails,
  mapVitalsToObservations
} from './fhir.data.js';

/**
 * Creates ABDM-compliant Observation resource for vital signs
 * @param {Object} vital - Vital sign details
 * @param {string} patientReference - Patient resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @returns {Object} FHIR Observation resource
 */
export const createABDMVitalSignObservationResource = (
  vital,
  patientReference,
  encounterReference,
  practitionerReference
) => ({
  resourceType: "Observation",
  id: `observation-vital-${Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Observation"]
  },
  identifier: [
    {
      use: "usual",
      system: "https://www.healtether.com/observation",
      value: `vital-${Date.now()}`
    }
  ],
  status: "final",
  category: [
    {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/observation-category",
          code: "vital-signs",
          display: "Vital Signs"
        }
      ]
    }
  ],
  code: {
    coding: [
      {
        system: "http://loinc.org",
        code: vital.loincCode || "85354-9",
        display: vital.display || "Vital sign"
      }
    ],
    text: vital.display || "Vital sign"
  },
  subject: {
    reference: patientReference,
    display: "Patient"
  },
  encounter: {
    reference: encounterReference,
    display: "Encounter"
  },
  effectiveDateTime: new Date().toISOString(),
  performer: [
    {
      reference: practitionerReference,
      display: "Practitioner"
    }
  ],
  valueQuantity: {
    value: vital.value,
    unit: vital.unit,
    system: "http://unitsofmeasure.org",
    code: vital.unitCode
  },
  interpretation: vital.interpretation ? [
    {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation",
          code: vital.interpretation,
          display: vital.interpretationDisplay || vital.interpretation
        }
      ]
    }
  ] : undefined
});

/**
 * Creates ABDM-compliant Composition resource for WellnessRecord
 * @param {string} patientReference - Patient resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @param {string} organizationReference - Organization resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {Array} observationReferences - Array of Observation resource references
 * @returns {Object} FHIR Composition resource
 */
export const createABDMWellnessCompositionResource = (
  patientReference,
  practitionerReference,
  organizationReference,
  encounterReference,
  observationReferences
) => ({
  resourceType: "Composition",
  id: `composition-wellness-${Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/WellnessRecord"]
  },
  identifier: {
    system: "https://www.healtether.com/wellness-composition",
    value: `wellness-comp-${Date.now()}`
  },
  status: "final",
  type: {
    text: "Wellness Record"
  },
  category: [
    {
      coding: [
        {
          system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-record-type",
          code: "wellness",
          display: "Wellness"
        }
      ]
    }
  ],
  subject: {
    reference: patientReference,
    display: "Patient"
  },
  encounter: {
    reference: encounterReference,
    display: "Encounter"
  },
  date: new Date().toISOString(),
  author: [
    {
      reference: practitionerReference,
      display: "Practitioner"
    }
  ],
  title: "Wellness Record",
  custodian: {
    reference: organizationReference,
    display: "Organization"
  },
  section: [
    // 1. Vital Signs Section
    {
      title: "Vital Signs",
      entry: observationReferences.map(ref => ({
        reference: ref,
        display: "Observation"
      }))
    },
    // 2. Body Measurement Section
    {
      title: "Body Measurement",
      entry: []
    },
    // 3. Physical Activity Section
    {
      title: "Physical Activity",
      entry: []
    },
    // 4. General Assessment Section
    {
      title: "General Assessment",
      entry: []
    },
    // 5. Women Health Section
    {
      title: "Women Health",
      entry: []
    },
    // 6. Lifestyle Section
    {
      title: "Lifestyle",
      entry: []
    },
    // 7. Other Observations Section
    {
      title: "Other Observations",
      entry: []
    },
    // 8. Document Reference Section
    {
      title: "Document Reference",
      entry: []
    }
  ]
});

/**
 * Creates ABDM-compliant WellnessRecord structure for NDHM FHIR R4 compliance
 * @param {string} artifact - Artifact type (e.g., "WellnessRecord")
 * @param {Object} patient - Patient data with ABHA information
 * @param {Object} practitionerData - Practitioner/doctor data
 * @param {Object} prescription - Prescription data with vitals
 * @param {Object} medicalHistory - Patient medical history
 * @param {Object} clinicData - Clinic/organization data
 * @param {Object} appointmentData - Appointment/encounter data
 * @returns {Object} ABDM-compliant WellnessRecord structure
 */
export const createABDMCompliantWellnessStructure = (
  artifact,
  patient,
  practitionerData,
  prescription,
  medicalHistory,
  clinicData,
  appointmentData
) => {
  try {
    // Create resource references following FHIR standards
    const patientReference = `Patient/${patient._id}`;
    const practitionerReference = `Practitioner/${practitionerData._id}`;
    const organizationReference = `Organization/${clinicData._id}`;
    const encounterReference = `Encounter/${appointmentData._id}`;

    // Map vitals to ABDM-compliant observations
    const observationResources = mapVitalsToObservations(prescription.vitals || {});
    const observationReferences = observationResources.map((obs, index) => `Observation/obs-${index}`);

    // Create ABDM-compliant Composition resource
    const compositionResource = createABDMWellnessCompositionResource(
      patientReference,
      practitionerReference,
      organizationReference,
      encounterReference,
      observationReferences
    );

    // Return ABDM-compliant structure for Communications service
    return {
      // General NDHM metadata
      general: createGeneralDetails(
        artifact,
        "https://www.healtether.com",
        ["hip1", "hip2"],
        "final",
        "SBX_003515"
      ),

      // Core healthcare entities
      patient: createPatientDetails(
        patient._id,
        patient.abhaNumber,
        patient.abhaAddress,
        {
          text: `${patient.firstName + patient.lastName}`,
          prefix: [`${patient.prefix}`],
        },
        patient.gender,
        patient.birthday,
        patient?.address,
        [`${practitionerData.firstName + practitionerData.lastName}`],
        medicalHistory?.allergies || [],
        patient.mobile
      ),
      practitioners: [createPractitionerDetails(practitionerData)],
      organization: createOrganizationDetails(clinicData),
      encounter: createEncounterDetails(appointmentData),

      // ABDM-compliant FHIR resources
      composition: compositionResource,
      observations: observationResources,

      // Digital signature for authenticity
      signature: createSignatureDetails("", [
        `${practitionerData.firstName + practitionerData.lastName}`,
      ]),
    };
  } catch (error) {
    console.error("Error creating ABDM-compliant Wellness structure:", error);
    throw new Error(`ABDM Wellness Creation Failed: ${error.message}`);
  }
};
