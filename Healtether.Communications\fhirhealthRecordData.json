{"fhirId": "e48f0384-209f-4313-b6e4-b0c4df552231", "general": {"artifact": "HealthDocumentRecord", "hipUrl": "https://www.healtether.com", "hipIds": ["hip1", "hip2"], "status": "final", "clientId": "SBX_003515", "_id": "68343f50882fb571401e07db"}, "patient": {"id": "6833ef863a9b880c1c09737e", "abhaNumber": "91-7573-2448-7649", "abhaAddress": "testraja@sbx", "name": {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prefix": ["undefined"]}, "gender": "male", "dob": "1999-10-09", "doctors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "allergyIntolerances": [], "telecom": [{"system": "phone", "value": "**********", "use": "mobile", "_id": "68343f50882fb571401e07dd"}], "address": [{"use": "home", "type": "physical", "text": "NO 12(2), VEE<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> STREET, THIRUMALAPURAM, BODINAYAKANUR, Bodinayackanur, Bodinayakanur, Theni, Tamil Nadu", "city": "THENI", "state": "TAMIL NADU", "district": "TAMIL NADU", "postalCode": "625513", "country": "india", "_id": "68343f50882fb571401e07de"}], "_id": "68343f50882fb571401e07dc"}, "practitioners": [{"names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "licenses": [{"code": "MD", "display": "Medical License number", "licNo": "1234567", "_id": "68343f50882fb571401e07e0"}], "patient": "patient123", "gender": "female", "birthDate": "2025-05-26", "address": [{"use": "home", "type": "physical", "text": "<PERSON><PERSON><PERSON> ganj", "postalCode": "474001", "country": "india", "_id": "68343f50882fb571401e07e1"}], "telecom": [{"system": "phone", "value": "**********", "use": "mobile", "_id": "68343f50882fb571401e07e2"}], "_id": "68343f50882fb571401e07df"}], "encounter": {"status": "finished", "startTime": "2025-05-26T09:52:03.121Z", "endTime": "2023-11-01T10:00:00+05:30", "_id": "68343f50882fb571401e07e3"}, "organization": {"name": "TEST", "telecom": [{"system": "phone", "value": "**********", "use": "work", "_id": "68343f50882fb571401e07e5"}], "licenses": [{"code": "PRN", "display": "Provider number", "licNo": "1234567", "_id": "68343f50882fb571401e07e6"}], "_id": "68343f50882fb571401e07e4"}, "documentReferences": [], "signature": {"who": {"type": "Practitioner", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sigFormat": "image/jpeg", "data": "c2lnbmF0dXJlIGRhdGEgaGVyZQ==", "_id": "68343f50882fb571401e07e7"}, "abhaCareContextLinked": false, "_id": "68343f50882fb571401e07da", "__v": 0}