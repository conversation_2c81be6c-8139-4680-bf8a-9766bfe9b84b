 

import {
  createGeneralDetails,
  createPatientDetails,
  createPractitionerDetails,
  createOrganizationDetails,
  createEncounterDetails,
  createSignatureDetails,
  createDocumentReference
} from './fhir.data.js';

/**
 * Creates ABDM-compliant Composition resource for HealthDocumentRecord
 * @param {string} patientReference - Patient resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @param {string} organizationReference - Organization resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {Array} documentReferences - Array of DocumentReference resource references
 * @returns {Object} FHIR Composition resource
 */
export const createABDMHealthDocumentCompositionResource = (
  patientReference,
  practitionerReference,
  organizationReference,
  encounterReference,
  documentReferences
) => ({
  resourceType: "Composition",
  id: `composition-healthdoc-${Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/HealthDocumentRecord"]
  },
  identifier: {
    system: "https://www.healtether.com/healthdoc-composition",
    value: `healthdoc-comp-${Date.now()}`
  },
  status: "final",
  type: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: "419891008",
        display: "Record artifact"
      }
    ]
  },
  category: [
    {
      coding: [
        {
          system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-record-type",
          code: "health-document",
          display: "Health Document"
        }
      ]
    }
  ],
  subject: {
    reference: patientReference,
    display: "Patient"
  },
  encounter: {
    reference: encounterReference,
    display: "Encounter"
  },
  date: new Date().toISOString(),
  author: [
    {
      reference: practitionerReference,
      display: "Practitioner"
    }
  ],
  title: "Health Document Record",
  custodian: {
    reference: organizationReference,
    display: "Organization"
  },
  section: [
    {
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "419891008",
            display: "Record artifact"
          }
        ]
      },
      entry: documentReferences.map(ref => ({
        reference: ref.reference,
        display: "Document Reference"
      }))
    }
  ]
});

/**
 * Creates ABDM-compliant HealthDocumentRecord structure for NDHM FHIR R4 compliance
 * @param {string} artifact - Artifact type (e.g., "HealthDocumentRecord")
 * @param {Object} clinicData - Clinic/organization data
 * @param {Object} appointmentData - Appointment/encounter data
 * @param {Object} patient - Patient data with ABHA information
 * @param {Object} practitionerData - Practitioner/doctor data
 * @returns {Object} ABDM-compliant HealthDocumentRecord structure
 */
export const createABDMCompliantHealthDocumentStructure = async (
  artifact,
  clinicData,
  appointmentData,
  patient,
  practitionerData
) => {
  try {
    // Create resource references following FHIR standards
    const patientReference = `Patient/${patient._id}`;
    const practitionerReference = `Practitioner/${practitionerData._id}`;
    const organizationReference = `Organization/${clinicData._id}`;
    const encounterReference = `Encounter/${appointmentData._id}`;

    // Process all document types
    const allDocuments = [
      ...(appointmentData.medicalRecords || []).map(doc => ({ ...doc, type: "medical" })),
      ...(appointmentData.procedureRecords || []).map(doc => ({ ...doc, type: "procedure" })),
      ...(appointmentData.prescriptionRecords || []).map(doc => ({ ...doc, type: "laboratory" }))
    ];

    // Create document references
    const documentReferencePromises = allDocuments.length > 0
      ? allDocuments
          .filter((item) => item.blobName)
          .map(async (item) => {
            const docRef = await createDocumentReference(
              item.blobName,
              clinicData._id,
              "Health Document",
              item.type === "laboratory" ? "Laboratory report" : "Medical document"
            );
            return {
              reference: `DocumentReference/doc-${item._id || Date.now()}`,
              type: item.type,
              resource: docRef
            };
          })
      : [];

    const documentReferences = await Promise.all(documentReferencePromises);
    const documentResources = documentReferences.map(ref => ref.resource);

    // Create ABDM-compliant Composition resource
    const compositionResource = createABDMHealthDocumentCompositionResource(
      patientReference,
      practitionerReference,
      organizationReference,
      encounterReference,
      documentReferences
    );

    // Return ABDM-compliant structure for Communications service
    return {
      // General NDHM metadata
      general: createGeneralDetails(
        artifact,
        "https://www.healtether.com",
        ["hip1", "hip2"],
        "final",
        "SBX_003515"
      ),

      // Core healthcare entities
      patient: createPatientDetails(
        patient._id,
        patient.abhaNumber,
        patient.abhaAddress,
        {
          text: `${patient.firstName + patient.lastName}`,
          prefix: [`${patient.prefix}`],
        },
        patient.gender,
        patient.birthday,
        patient?.address,
        [`${practitionerData.firstName + practitionerData.lastName}`],
        [],
        patient.mobile
      ),
      practitioners: [createPractitionerDetails(practitionerData)],
      organization: createOrganizationDetails(clinicData),
      encounter: createEncounterDetails(appointmentData),

      // ABDM-compliant FHIR resources
      composition: compositionResource,
      documentReferences: documentResources,

      // Digital signature for authenticity
      signature: createSignatureDetails("", [
        `${practitionerData.firstName + practitionerData.lastName}`,
      ]),
    };
  } catch (error) {
    console.error("Error creating ABDM-compliant HealthDocument structure:", error);
    throw new Error(`ABDM HealthDocument Creation Failed: ${error.message}`);
  }
};
