 

import {
  createGeneralDetails,
  createPatientDetails,
  createPractitionerDetails,
  createOrganizationDetails,
  createEncounterDetails,
  createSignatureDetails
} from './fhir.data.js';

/**
 * Creates ABDM-compliant Procedure resource
 * @param {Object} procedure - Procedure details
 * @param {string} patientReference - Patient resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @returns {Object} FHIR Procedure resource
 */
export const createABDMProcedureResource = (
  procedure,
  patientReference,
  encounterReference,
  practitionerReference
) => ({
  resourceType: "Procedure",
  id: `procedure-${procedure._id || Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Procedure"]
  },
  identifier: [
    {
      use: "usual",
      system: "https://www.healtether.com/procedure",
      value: procedure._id?.toString() || `procedure-${Date.now()}`
    }
  ],
  status: "completed",
  category: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: "387713003",
        display: "Surgical procedure"
      }
    ]
  },
  code: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: procedure.snomedCode || "71388002",
        display: procedure.name || "Procedure"
      }
    ],
    text: procedure.name || "Procedure"
  },
  subject: {
    reference: patientReference,
    display: "Patient"
  },
  encounter: {
    reference: encounterReference,
    display: "Encounter"
  },
  performedDateTime: procedure.performedDate || new Date().toISOString(),
  performer: [
    {
      function: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "304292004",
            display: "Surgeon"
          }
        ]
      },
      actor: {
        reference: practitionerReference,
        display: "Practitioner"
      }
    }
  ],
  outcome: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: "385669000",
        display: "Successful"
      }
    ]
  },
  followUp: procedure.followUp ? [
    {
      text: procedure.followUp
    }
  ] : undefined,
  note: procedure.notes ? [
    {
      text: procedure.notes,
      time: new Date().toISOString()
    }
  ] : undefined
});

/**
 * Creates ABDM-compliant Composition resource for DischargeSummaryRecord
 * @param {string} patientReference - Patient resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @param {string} organizationReference - Organization resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {Array} conditionReferences - Array of Condition resource references
 * @param {Array} procedureReferences - Array of Procedure resource references
 * @param {Array} medicationReferences - Array of MedicationStatement resource references
 * @returns {Object} FHIR Composition resource
 */
export const createABDMDischargeSummaryCompositionResource = (
  patientReference,
  practitionerReference,
  organizationReference,
  encounterReference,
  conditionReferences,
  procedureReferences,
  medicationReferences
) => ({
  resourceType: "Composition",
  id: `composition-discharge-${Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DischargeSummaryRecord"]
  },
  identifier: {
    system: "https://www.healtether.com/discharge-composition",
    value: `discharge-comp-${Date.now()}`
  },
  status: "final",
  type: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: "373942005",
        display: "Discharge summary"
      }
    ]
  },
  category: [
    {
      coding: [
        {
          system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-record-type",
          code: "discharge-summary",
          display: "Discharge Summary"
        }
      ]
    }
  ],
  subject: {
    reference: patientReference,
    display: "Patient"
  },
  encounter: {
    reference: encounterReference,
    display: "Encounter"
  },
  date: new Date().toISOString(),
  author: [
    {
      reference: practitionerReference,
      display: "Practitioner"
    }
  ],
  title: "Discharge Summary Record",
  custodian: {
    reference: organizationReference,
    display: "Organization"
  },
  section: [
    // 1. Chief Complaints Section
    {
      title: "Chief Complaints",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "422843007",
            display: "Chief complaint section"
          }
        ]
      },
      entry: conditionReferences.map(ref => ({
        reference: ref,
        display: "Condition"
      }))
    },
    // 2. Physical Examination Section
    {
      title: "Physical Examination",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "425044008",
            display: "Physical examination section"
          }
        ]
      },
      entry: []
    },
    // 3. Allergies Section
    {
      title: "Allergies",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "722446000",
            display: "Allergy record"
          }
        ]
      },
      entry: []
    },
    // 4. Medical History Section
    {
      title: "Medical History",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "371529009",
            display: "History and physical report"
          }
        ]
      },
      entry: []
    },
    // 5. Family History Section
    {
      title: "Family History",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "422432008",
            display: "Family history section"
          }
        ]
      },
      entry: []
    },
    // 6. Investigations Section
    {
      title: "Investigations",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "721963009",
            display: "Order document"
          }
        ]
      },
      entry: []
    },
    // 7. Medications Section
    {
      title: "Medications",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "422979000",
            display: "Medication section"
          }
        ]
      },
      entry: medicationReferences.map(ref => ({
        reference: ref,
        display: "Medication Statement"
      }))
    },
    // 8. Procedures Section
    {
      title: "Procedures",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "371525003",
            display: "Clinical procedure report"
          }
        ]
      },
      entry: procedureReferences.map(ref => ({
        reference: ref,
        display: "Procedure"
      }))
    },
    // 9. Care Plan Section
    {
      title: "Care Plan",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "734163000",
            display: "Care plan"
          }
        ]
      },
      entry: []
    }
  ]
});

/**
 * Creates ABDM-compliant DischargeSummaryRecord structure for NDHM FHIR R4 compliance
 * @param {string} artifact - Artifact type (e.g., "DischargeSummaryRecord")
 * @param {Object} clinicData - Clinic/organization data
 * @param {Object} patient - Patient data with ABHA information
 * @param {Object} appointmentData - Appointment/encounter data
 * @param {Object} practitionerData - Practitioner/doctor data
 * @param {Object} prescription - Prescription data with medications, diagnosis, etc.
 * @param {Object} medicalHistory - Patient medical history
 * @returns {Object} ABDM-compliant DischargeSummaryRecord structure
 */
export const createABDMCompliantDischargeSummaryStructure = (
  artifact,
  clinicData,
  patient,
  appointmentData,
  practitionerData,
  prescription,
  medicalHistory
) => {
  try {
    // Create resource references following FHIR standards
    const patientReference = `Patient/${patient._id}`;
    const practitionerReference = `Practitioner/${practitionerData._id}`;
    const organizationReference = `Organization/${clinicData._id}`;
    const encounterReference = `Encounter/${appointmentData._id}`;

    // Import condition creation from OPConsult module
    const { createABDMConditionResource } = require('./abdm-opconsult.fhir.js');
    const { createABDMMedicationStatementResource } = require('./abdm-prescription.fhir.js');

    // Create ABDM-compliant Condition resources for diagnosis
    const conditionResources = prescription.prescriptions?.diagnosis?.map(diagnosis =>
      createABDMConditionResource(
        diagnosis,
        patientReference,
        encounterReference,
        practitionerReference
      )
    ) || [];

    // Create ABDM-compliant Procedure resources
    const procedureResources = medicalHistory.pastProcedureHistory?.map(procedure =>
      createABDMProcedureResource(
        procedure,
        patientReference,
        encounterReference,
        practitionerReference
      )
    ) || [];

    // Create ABDM-compliant MedicationStatement resources
    const medicationResources = prescription.prescriptions?.drugPrescriptions?.map(medication =>
      createABDMMedicationStatementResource(
        medication,
        patientReference,
        encounterReference,
        practitionerReference
      )
    ) || [];

    // Create resource reference arrays
    const conditionReferences = conditionResources.map(resource => `Condition/${resource.id}`);
    const procedureReferences = procedureResources.map(resource => `Procedure/${resource.id}`);
    const medicationReferences = medicationResources.map(resource => `MedicationStatement/${resource.id}`);

    // Create ABDM-compliant Composition resource
    const compositionResource = createABDMDischargeSummaryCompositionResource(
      patientReference,
      practitionerReference,
      organizationReference,
      encounterReference,
      conditionReferences,
      procedureReferences,
      medicationReferences
    );

    // Return ABDM-compliant structure for Communications service
    return {
      // General NDHM metadata
      general: createGeneralDetails(
        artifact,
        "https://www.healtether.com",
        ["hip1", "hip2"],
        "final",
        "SBX_003515"
      ),

      // Core healthcare entities
      patient: createPatientDetails(
        patient._id,
        patient.abhaNumber,
        patient.abhaAddress,
        {
          text: `${patient.firstName + patient.lastName}`,
          prefix: [`${patient.prefix}`],
        },
        patient.gender,
        patient.birthday,
        patient?.address,
        [`${practitionerData.firstName + practitionerData.lastName}`],
        medicalHistory.allergies || [],
        patient.mobile
      ),
      practitioners: [createPractitionerDetails(practitionerData)],
      organization: createOrganizationDetails(clinicData),
      encounter: createEncounterDetails(appointmentData),

      // ABDM-compliant FHIR resources
      composition: compositionResource,
      conditions: conditionResources,
      procedures: procedureResources,
      medicationStatements: medicationResources,

      // Digital signature for authenticity
      signature: createSignatureDetails("", [
        `${practitionerData.firstName + practitionerData.lastName}`,
      ]),
    };
  } catch (error) {
    console.error("Error creating ABDM-compliant DischargeSummary structure:", error);
    throw new Error(`ABDM DischargeSummary Creation Failed: ${error.message}`);
  }
};
