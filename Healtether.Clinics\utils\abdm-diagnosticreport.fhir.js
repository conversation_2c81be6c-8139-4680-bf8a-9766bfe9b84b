 

import { 
  createPatientDetails, 
  createPractitionerDetails, 
  createOrganizationDetails, 
  createEncounterDetails,
  createGeneralDetails,
  createSignatureDetails,
  createBinaryDetails,
  fetchImageAsBase64,
  createDocumentReference
} from "./abdm-fhir-common.js";

/**
 * Creates ABDM-compliant DiagnosticReport resource (Lab or Imaging)
 * @param {Object} reportData - Diagnostic report data
 * @param {string} patientReference - Patient resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @param {string} organizationReference - Organization resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {string} type - Report type: "lab" or "imaging"
 * @returns {Object} FHIR DiagnosticReport resource
 */
export const createABDMDiagnosticReportResource = (
  reportData,
  patientReference,
  practitionerReference,
  organizationReference,
  encounterReference,
  type = "lab"
) => ({
  resourceType: "DiagnosticReport",
  id: `diagnostic-report-${type}-${Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: [
      type === "lab" 
        ? "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DiagnosticReportLab"
        : "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DiagnosticReportImaging"
    ]
  },
  identifier: [
    {
      system: "https://www.healtether.com/diagnostic-report",
      value: reportData.reportId || `report-${Date.now()}`
    }
  ],
  status: "final",
  category: [
    {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/v2-0074",
          code: type === "lab" ? "LAB" : "RAD",
          display: type === "lab" ? "Laboratory" : "Radiology"
        }
      ]
    }
  ],
  code: {
    coding: [
      {
        system: "http://loinc.org",
        code: reportData.loincCode || (type === "lab" ? "33747-0" : "18748-4"),
        display: reportData.testName || (type === "lab" ? "General laboratory studies" : "Diagnostic imaging study")
      }
    ],
    text: reportData.testName || "Diagnostic Report"
  },
  subject: {
    reference: patientReference
  },
  encounter: {
    reference: encounterReference
  },
  effectiveDateTime: reportData.reportDate || new Date().toISOString(),
  issued: reportData.issuedDate || new Date().toISOString(),
  performer: [
    {
      reference: organizationReference
    }
  ],
  result: reportData.observations?.map((obs, index) => ({
    reference: `Observation/obs-${index}`
  })) || [],
  conclusion: reportData.conclusion || reportData.interpretation,
  conclusionCode: reportData.conclusionCodes?.map(code => ({
    coding: [
      {
        system: "http://snomed.info/sct",
        code: code.code,
        display: code.display
      }
    ]
  })) || []
});

/**
 * Creates ABDM-compliant Observation resource for lab/imaging results
 * @param {Object} observationData - Observation data
 * @param {string} patientReference - Patient resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @param {string} organizationReference - Organization resource reference
 * @returns {Object} FHIR Observation resource
 */
export const createABDMDiagnosticObservationResource = (
  observationData,
  patientReference,
  practitionerReference,
  organizationReference
) => ({
  resourceType: "Observation",
  id: `obs-diagnostic-${Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Observation"]
  },
  status: "final",
  category: [
    {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/observation-category",
          code: "laboratory",
          display: "Laboratory"
        }
      ]
    }
  ],
  code: {
    coding: [
      {
        system: "http://loinc.org",
        code: observationData.loincCode || "33747-0",
        display: observationData.testName || "Laboratory test"
      }
    ],
    text: observationData.testName
  },
  subject: {
    reference: patientReference
  },
  effectiveDateTime: observationData.testDate || new Date().toISOString(),
  performer: [
    {
      reference: organizationReference
    }
  ],
  valueQuantity: observationData.value ? {
    value: parseFloat(observationData.value),
    unit: observationData.unit || "mg/dL",
    system: "http://unitsofmeasure.org",
    code: observationData.ucumCode || "mg/dL"
  } : undefined,
  valueString: observationData.textValue,
  interpretation: observationData.interpretation ? [
    {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation",
          code: observationData.interpretation,
          display: observationData.interpretationDisplay
        }
      ]
    }
  ] : undefined,
  referenceRange: observationData.referenceRange ? [
    {
      low: observationData.referenceRange.low ? {
        value: parseFloat(observationData.referenceRange.low),
        unit: observationData.unit || "mg/dL"
      } : undefined,
      high: observationData.referenceRange.high ? {
        value: parseFloat(observationData.referenceRange.high),
        unit: observationData.unit || "mg/dL"
      } : undefined,
      text: observationData.referenceRange.text
    }
  ] : undefined
});

/**
 * Creates ABDM-compliant Composition resource for DiagnosticReportRecord
 * @param {string} patientReference - Patient resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @param {string} organizationReference - Organization resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {Array} diagnosticReportReferences - Array of DiagnosticReport resource references
 * @param {Array} documentReferences - Array of DocumentReference resource references
 * @param {string} reportType - Type of diagnostic report ("lab" or "imaging")
 * @returns {Object} FHIR Composition resource compliant with ABDM DiagnosticReportRecord
 */
export const createABDMDiagnosticReportCompositionResource = (
  patientReference,
  practitionerReference,
  organizationReference,
  encounterReference,
  diagnosticReportReferences,
  documentReferences,
  reportType = "lab"
) => ({
  resourceType: "Composition",
  id: `composition-diagnostic-${reportType}-${Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DiagnosticReportRecord"]
  },
  identifier: {
    system: "https://www.healtether.com/diagnostic-composition",
    value: `diagnostic-comp-${Date.now()}`
  },
  status: "final",
  // ABDM Required: Fixed SNOMED code for DiagnosticReportRecord
  type: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: reportType === "lab" ? "4241000179101" : "4201000179104",
        display: reportType === "lab" ? "Laboratory report" : "Imaging report"
      }
    ],
    text: reportType === "lab" ? "Laboratory report" : "Imaging report"
  },
  subject: {
    reference: patientReference
  },
  encounter: {
    reference: encounterReference
  },
  date: new Date().toISOString(),
  author: [
    {
      reference: practitionerReference
    }
  ],
  title: reportType === "lab" ? "Laboratory Report" : "Imaging Report",
  custodian: {
    reference: organizationReference
  },
  // ABDM Required: Single section with DiagnosticReport and DocumentReference entries
  section: [
    {
      title: reportType === "lab" ? "Laboratory Results" : "Imaging Results",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: reportType === "lab" ? "4241000179101" : "4201000179104",
            display: reportType === "lab" ? "Laboratory report" : "Imaging report"
          }
        ]
      },
      entry: [
        // DiagnosticReport entries
        ...diagnosticReportReferences.map(ref => ({
          reference: ref,
          type: "DiagnosticReport"
        })),
        // DocumentReference entries
        ...documentReferences.map(ref => ({
          reference: ref,
          type: "DocumentReference"
        }))
      ]
    }
  ]
});

/**
 * Creates ABDM-compliant DocumentBundle for DiagnosticReport records
 * @param {Object} compositionResource - FHIR Composition resource
 * @param {Object} patientResource - FHIR Patient resource
 * @param {Object} practitionerResource - FHIR Practitioner resource
 * @param {Object} organizationResource - FHIR Organization resource
 * @param {Object} encounterResource - FHIR Encounter resource
 * @param {Array} diagnosticReportResources - Array of FHIR DiagnosticReport resources
 * @param {Array} observationResources - Array of FHIR Observation resources
 * @param {Array} documentReferences - Array of FHIR DocumentReference resources
 * @returns {Object} ABDM-compliant DocumentBundle
 */
export const createABDMDiagnosticReportDocumentBundle = (
  compositionResource,
  patientResource,
  practitionerResource,
  organizationResource,
  encounterResource,
  diagnosticReportResources,
  observationResources,
  documentReferences
) => ({
  resourceType: "Bundle",
  id: `diagnostic-bundle-${Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle"]
  },
  identifier: {
    system: "https://www.healtether.com/bundle",
    value: `diagnostic-bundle-${Date.now()}`
  },
  type: "document",
  timestamp: new Date().toISOString(),
  entry: [
    // 1. Composition (must be first entry in document bundle)
    {
      fullUrl: `Composition/${compositionResource.id}`,
      resource: compositionResource
    },
    // 2. Patient
    {
      fullUrl: `Patient/${patientResource.id}`,
      resource: patientResource
    },
    // 3. Practitioner
    {
      fullUrl: `Practitioner/${practitionerResource.id}`,
      resource: practitionerResource
    },
    // 4. Organization
    {
      fullUrl: `Organization/${organizationResource.id}`,
      resource: organizationResource
    },
    // 5. Encounter
    {
      fullUrl: `Encounter/${encounterResource.id}`,
      resource: encounterResource
    },
    // 6. DiagnosticReports
    ...diagnosticReportResources.map(report => ({
      fullUrl: `DiagnosticReport/${report.id}`,
      resource: report
    })),
    // 7. Observations
    ...observationResources.map(observation => ({
      fullUrl: `Observation/${observation.id}`,
      resource: observation
    })),
    // 8. DocumentReferences
    ...documentReferences.map(docRef => ({
      fullUrl: `DocumentReference/${docRef.id}`,
      resource: docRef
    }))
  ]
});
