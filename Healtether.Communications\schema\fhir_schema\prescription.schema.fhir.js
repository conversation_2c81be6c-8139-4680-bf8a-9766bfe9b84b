import { Schema } from 'mongoose';

const telecomSchema = new Schema({
    system: String,
    value: String,
    use: String
});

const licenseSchema = new Schema({
    code: String,
    display: String,
    licNo: String
});

// ABDM-compliant coding schema
const codingSchema = new Schema(
  {
    system: { type: String },
    code: { type: String },
    display: { type: String }
  },
  { _id: false }
);

// ABDM-compliant CodeableConcept schema
const codeableConceptSchema = new Schema(
  {
    coding: [codingSchema],
    text: { type: String }
  },
  { _id: false }
);

// ABDM-compliant Reference schema
const referenceSchema = new Schema(
  {
    reference: { type: String },
    display: { type: String }
  },
  { _id: false }
);

// ABDM-compliant Quantity schema
const quantitySchema = new Schema(
  {
    value: { type: Number },
    unit: { type: String },
    system: { type: String },
    code: { type: String }
  },
  { _id: false }
);

// ABDM-compliant condition schema
const conditionSchema = new Schema({
    resourceType: { type: String, default: "Condition" },
    id: String,
    meta: {
        versionId: String,
        lastUpdated: String,
        profile: [String]
    },
    identifier: [{
        use: String,
        system: String,
        value: String
    }],
    clinicalStatus: codeableConceptSchema,
    verificationStatus: codeableConceptSchema,
    category: [codeableConceptSchema],
    severity: codeableConceptSchema,
    code: codeableConceptSchema,
    bodySite: [codeableConceptSchema],
    subject: referenceSchema,
    encounter: referenceSchema,
    onsetDateTime: String,
    abatementDateTime: String,
    recordedDate: String,
    recorder: referenceSchema,
    asserter: referenceSchema,
    stage: [{
        summary: codeableConceptSchema,
        assessment: [referenceSchema],
        type: codeableConceptSchema
    }],
    evidence: [{
        code: [codeableConceptSchema],
        detail: [referenceSchema]
    }],
    note: [{
        authorString: String,
        time: String,
        text: String
    }]
});

// ABDM-compliant dosage instruction schema
const dosageInstructionSchema = new Schema({
    sequence: Number,
    text: String,
    additionalInstruction: [codeableConceptSchema],
    timing: {
        repeat: {
            frequency: Number,
            period: Number,
            periodUnit: String,
            when: [String]
        }
    },
    route: codeableConceptSchema,
    site: codeableConceptSchema,
    method: codeableConceptSchema,
    doseAndRate: [{
        type: codeableConceptSchema,
        doseQuantity: quantitySchema,
        rateQuantity: quantitySchema
    }]
});

// ABDM-compliant medication request schema
const medicationRequestSchema = new Schema({
    resourceType: { type: String, default: "MedicationRequest" },
    id: String,
    meta: {
        versionId: String,
        lastUpdated: String,
        profile: [String]
    },
    identifier: [{
        use: String,
        system: String,
        value: String
    }],
    status: String,
    intent: String,
    category: [codeableConceptSchema],
    medicationCodeableConcept: codeableConceptSchema,
    subject: referenceSchema,
    encounter: referenceSchema,
    authoredOn: String,
    requester: referenceSchema,
    reasonCode: [codeableConceptSchema],
    reasonReference: [referenceSchema],
    dosageInstruction: [dosageInstructionSchema],
    dispenseRequest: {
        validityPeriod: {
            start: String,
            end: String
        },
        numberOfRepeatsAllowed: Number,
        quantity: quantitySchema,
        expectedSupplyDuration: quantitySchema
    }
});


const signatureSchema = new Schema({
    who: {
        type: { type: String },
        name: String
    },
    sigFormat: String,
    data: String
});

const addressSchema = new Schema({
    use: String,
    type: String,
    text: String,
    // line: [String],
    city: String,
    state: String,
    district: String,
    postalCode: String,
    country: String
});

const allergyIntoleranceSchema = new Schema({
    type: String,
    clinicalStatus: String,
    verificationStatus: String,
    doctor: String,
    notes: [String]
});

const patientSchema = new Schema({
    id:String,
    abhaNumber: String,
    abhaAddress: String,
    name: {
        text:String,
        prefix:[String]
    },
    gender: String,
    dob: String,
    doctors: [String],
    allergyIntolerances: [allergyIntoleranceSchema],
    telecom: [telecomSchema],
    address:[addressSchema]
});

const generalSchema = new Schema({
    artifact: String,
    hipUrl: String,
    hipIds: [String],
    status: String,
    clientId: String
});

const practitionerSchema = new Schema({
    names: [String],
    licenses: [licenseSchema],
    patient: String,
    gender: String,
    birthDate: String,
    address: [addressSchema],
    telecom: [telecomSchema],
});

const encounterSchema = new Schema({
    status: String,
    startTime: String,
    endTime: String
});


const organizationSchema = new Schema({
    name: String,
    telecom: [telecomSchema],
    licenses: [licenseSchema]
});

// ABDM-compliant Binary schema
const binarySchema = new Schema({
    resourceType: { type: String, default: "Binary" },
    id: String,
    meta: {
        versionId: String,
        lastUpdated: String,
        profile: [String]
    },
    contentType: String,
    data: String
});

// ABDM-compliant Procedure schema
const procedureSchema = new Schema({
    resourceType: { type: String, default: "Procedure" },
    id: String,
    meta: {
        versionId: String,
        lastUpdated: String,
        profile: [String]
    },
    identifier: [{
        use: String,
        system: String,
        value: String
    }],
    instantiatesCanonical: [String],
    instantiatesUri: [String],
    basedOn: [referenceSchema],
    partOf: [referenceSchema],
    status: String,
    statusReason: codeableConceptSchema,
    category: codeableConceptSchema,
    code: codeableConceptSchema,
    subject: referenceSchema,
    encounter: referenceSchema,
    performedDateTime: String,
    performedPeriod: {
        start: String,
        end: String
    },
    recorder: referenceSchema,
    asserter: referenceSchema,
    performer: [{
        function: codeableConceptSchema,
        actor: referenceSchema,
        onBehalfOf: referenceSchema
    }],
    location: referenceSchema,
    reasonCode: [codeableConceptSchema],
    reasonReference: [referenceSchema],
    bodySite: [codeableConceptSchema],
    outcome: codeableConceptSchema,
    report: [referenceSchema],
    complication: [codeableConceptSchema],
    complicationDetail: [referenceSchema],
    followUp: [codeableConceptSchema],
    note: [{
        authorString: String,
        time: String,
        text: String
    }],
    focalDevice: [{
        action: codeableConceptSchema,
        manipulated: referenceSchema
    }],
    usedReference: [referenceSchema],
    usedCode: [codeableConceptSchema]
})

const PrescriptionRecordSchema = new Schema({
    fhirId: {
        type: String,
        required: true,
        index: true
    },
    general: generalSchema,
    patient: patientSchema,
    encounter: encounterSchema,
    practitioners: [practitionerSchema],
    conditions: [conditionSchema],
    organization: organizationSchema,
    medicationRequests: [medicationRequestSchema],
    procedure:procedureSchema,
    binary: binarySchema,
    signature: signatureSchema,
    abhaCareContextLinked:{
        type: Boolean,
        default: false
    }
});

export { PrescriptionRecordSchema };
