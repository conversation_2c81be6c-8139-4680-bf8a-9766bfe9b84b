import { Schema } from 'mongoose';

const telecomSchema = new Schema({
    system: String,
    value: String,
    use: String
});

const licenseSchema = new Schema({
    code: String,
    display: String,
    licNo: String
});

// ABDM-compliant coding schema
const codingSchema = new Schema(
  {
    system: { type: String },
    code: { type: String },
    display: { type: String }
  },
  { _id: false }
);

// ABDM-compliant CodeableConcept schema
const codeableConceptSchema = new Schema(
  {
    coding: [codingSchema],
    text: { type: String }
  },
  { _id: false }
);

// ABDM-compliant Reference schema
const referenceSchema = new Schema(
  {
    reference: { type: String },
    display: { type: String }
  },
  { _id: false }
);

// ABDM-compliant Quantity schema
const quantitySchema = new Schema(
  {
    value: { type: Number },
    unit: { type: String },
    system: { type: String },
    code: { type: String }
  },
  { _id: false }
);

const attachmentSchema = new Schema({
    contentType: String,
    language: String,
    data: String,
    title: String,
    creation: String
});

const signatureSchema = new Schema({
    who: {
        type: { type: String },
        name: String
    },
    sigFormat: String,
    data: String
});

const addressSchema = new Schema({
    use: String,
    type: String,
    text: String,
    line: [String],
    city: String,
    state: String,
    district: String,
    postalCode: String,
    country: String
});

const patientSchema = new Schema({
    id:String,
    abhaNumber: String,
    abhaAddress: String,
    name: String,
    gender: String,
    dob: String,
    doctors: [String],
    telecom: [telecomSchema],
    address:[addressSchema]
});

const generalSchema = new Schema({
    artifact: String,
    hipUrl: String,
    hipIds: [String],
    status: String,
    clientId: String
});

const practitionerSchema = new Schema({
    names: [String],
    licenses: [licenseSchema],
    patient: String,
    gender: String,
    birthDate: String,
    address: [addressSchema],
    telecom: [telecomSchema],
});
const organizationSchema = new Schema({
    name: String,
    telecom: [telecomSchema],
    licenses: [licenseSchema]
});

// ABDM-compliant DiagnosticReport schema
const diagnosticReportsSchema = new Schema({
    resourceType: { type: String, default: "DiagnosticReport" },
    id: String,
    meta: {
        versionId: String,
        lastUpdated: String,
        profile: [String]
    },
    identifier: [{
        use: String,
        system: String,
        value: String
    }],
    basedOn: [referenceSchema],
    status: String,
    category: [codeableConceptSchema],
    code: codeableConceptSchema,
    subject: referenceSchema,
    encounter: referenceSchema,
    effectiveDateTime: String,
    effectivePeriod: {
        start: String,
        end: String
    },
    issued: String,
    performer: [referenceSchema],
    resultsInterpreter: [referenceSchema],
    specimen: [referenceSchema],
    result: [referenceSchema],
    imagingStudy: [referenceSchema],
    media: [{
        comment: String,
        link: referenceSchema
    }],
    conclusion: String,
    conclusionCode: [codeableConceptSchema],
    presentedForm: [{ attachment: attachmentSchema }]
});


const DiagnosticReportSchema = new Schema({
    fhirId: {
        type: String,
        required: true,
        index: true
    },
    general: generalSchema,
    patient: patientSchema,
    practitioners: [practitionerSchema],
    organization: organizationSchema,
    diagnosticReports:[diagnosticReportsSchema],
    signature: signatureSchema,
    abhaCareContextLinked:{
        type: Boolean,
        default: false
    }
});

export { DiagnosticReportSchema };
