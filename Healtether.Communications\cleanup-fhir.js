// Emergency FHIR Records Cleanup Script
import mongoose from 'mongoose';
import dotenv from 'dotenv';

dotenv.config();

const MONGO_URI = process.env.MONGODB_PORTAL_URI || "mongodb+srv://user-tst:<EMAIL>/doctor-app-tst?retryWrites=true&w=majority";

async function cleanupFhirRecords() {
  console.log('🚨 EMERGENCY FHIR Records Cleanup');
  console.log('=================================');
  console.log('MongoDB URI:', MONGO_URI ? 'Found' : 'Not found');
  console.log('Environment variables loaded:', !!process.env.MONGODB_PORTAL_URI);

  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGO_URI);
    console.log('✅ Connected successfully');

    const db = mongoose.connection.db;

    // Show current size
    const stats = await db.stats();
    console.log(`\nCurrent database size: ${(stats.dataSize / (1024 * 1024)).toFixed(2)} MB`);
    console.log(`Storage size: ${(stats.storageSize / (1024 * 1024)).toFixed(2)} MB`);

    // FHIR collections to clean
    const collections = [
      'prescriptionfhirrecords',
      'healthdocumentfhirrecords',
      'opconsultfhirrecords',
      'immunizationreportfhirrecords',
      'diagnosticreportfhirrecords',
      'dischargesummaryfhirrecords',
      'wellnessreportfhirrecords',
      'invoicereportfhirrecords'
    ];

    let totalDeleted = 0;

    console.log('\n📋 Current Collection Sizes:');
    console.log('============================');

    // Show current collection sizes
    for (const collectionName of collections) {
      try {
        const collection = db.collection(collectionName);
        const count = await collection.countDocuments();
        if (count > 0) {
          const collStats = await db.command({ collStats: collectionName });
          const sizeMB = (collStats.size / (1024 * 1024)).toFixed(2);
          console.log(`${collectionName}: ${count} records, ${sizeMB} MB`);
        } else {
          console.log(`${collectionName}: 0 records`);
        }
      } catch (error) {
        console.log(`${collectionName}: Collection not found`);
      }
    }

    // Delete records older than 7 days (aggressive cleanup)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    console.log(`\n🧹 Deleting records older than: ${sevenDaysAgo.toISOString()}`);
    console.log('='.repeat(60));

    for (const collectionName of collections) {
      try {
        const collection = db.collection(collectionName);

        // Try multiple date field patterns
        const queries = [
          { "created.on": { $lt: sevenDaysAgo } },
          { "createdAt": { $lt: sevenDaysAgo } },
          { "created": { $lt: sevenDaysAgo } },
          { "_id": { $lt: mongoose.Types.ObjectId.createFromTime(sevenDaysAgo.getTime() / 1000) } }
        ];

        let deleted = 0;
        for (const query of queries) {
          const count = await collection.countDocuments(query);
          if (count > 0) {
            console.log(`\n${collectionName}: Deleting ${count} old records...`);
            const result = await collection.deleteMany(query);
            deleted = result.deletedCount;
            console.log(`✅ Deleted ${deleted} records`);
            totalDeleted += deleted;
            break;
          }
        }

        if (deleted === 0) {
          console.log(`${collectionName}: No old records found`);
        }
      } catch (error) {
        console.log(`⚠️  Error with ${collectionName}: ${error.message}`);
      }
    }

    // If still not enough space, delete more aggressively
    const newStats = await db.stats();
    const currentSizeMB = newStats.dataSize / (1024 * 1024);

    if (currentSizeMB > 500) { // Still over 500MB
      console.log(`\n⚠️  Still using ${currentSizeMB.toFixed(2)} MB. Deleting ALL FHIR records...`);
      console.log('='.repeat(60));

      for (const collectionName of collections) {
        try {
          const collection = db.collection(collectionName);
          const count = await collection.countDocuments();

          if (count > 0) {
            console.log(`${collectionName}: Deleting ALL ${count} records...`);
            const result = await collection.deleteMany({});
            console.log(`✅ Deleted ${result.deletedCount} records`);
            totalDeleted += result.deletedCount;
          }
        } catch (error) {
          console.log(`⚠️  Error with ${collectionName}: ${error.message}`);
        }
      }
    }

    // Final stats
    const finalStats = await db.stats();
    const finalSizeMB = finalStats.dataSize / (1024 * 1024);
    const freedMB = (stats.dataSize - finalStats.dataSize) / (1024 * 1024);

    console.log('\n📊 Cleanup Results:');
    console.log('==================');
    console.log(`Total records deleted: ${totalDeleted}`);
    console.log(`Space freed: ${freedMB.toFixed(2)} MB`);
    console.log(`Final database size: ${finalSizeMB.toFixed(2)} MB`);
    console.log(`Final storage size: ${(finalStats.storageSize / (1024 * 1024)).toFixed(2)} MB`);

    if (finalSizeMB < 500) {
      console.log('✅ Database is now under 500MB limit!');
    } else {
      console.log('⚠️  Database is still over 500MB. Consider upgrading your MongoDB plan.');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n✅ Cleanup completed');
  }
}

// Run the cleanup
cleanupFhirRecords().catch(console.error);
