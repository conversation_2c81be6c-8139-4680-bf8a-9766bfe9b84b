 

import {
  createGeneralDetails,
  createPatientDetails,
  createPractitionerDetails,
  createOrganizationDetails,
  createEncounterDetails,
  createSignatureDetails,
  createDocumentReference
} from './fhir.data.js';

/**
 * Creates ABDM-compliant Immunization resource
 * @param {Object} immunization - Immunization details
 * @param {string} patientReference - Patient resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @returns {Object} FHIR Immunization resource
 */
export const createABDMImmunizationResource = (
  immunization,
  patientReference,
  encounterReference,
  practitionerReference
) => ({
  resourceType: "Immunization",
  id: `immunization-${immunization._id || Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Immunization"]
  },
  identifier: [
    {
      use: "usual",
      system: "https://www.healtether.com/immunization",
      value: immunization._id?.toString() || `immunization-${Date.now()}`
    }
  ],
  status: "completed",
  vaccineCode: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: immunization.snomedCode || "396429000",
        display: immunization.vaccineName || "Vaccine"
      }
    ],
    text: immunization.vaccineName || "Vaccine"
  },
  patient: {
    reference: patientReference,
    display: "Patient"
  },
  encounter: {
    reference: encounterReference,
    display: "Encounter"
  },
  occurrenceDateTime: immunization.administeredDate || new Date().toISOString(),
  recorded: new Date().toISOString(),
  primarySource: true,
  manufacturer: {
    display: immunization.manufacturer || "Unknown"
  },
  lotNumber: immunization.lotNumber || "Unknown",
  expirationDate: immunization.expirationDate,
  site: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: immunization.siteCode || "72098002",
        display: immunization.site || "Entire upper arm"
      }
    ]
  },
  route: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: immunization.routeCode || "78421000",
        display: immunization.route || "Intramuscular route"
      }
    ]
  },
  doseQuantity: {
    value: immunization.doseQuantity || 0.5,
    unit: immunization.doseUnit || "mL",
    system: "http://unitsofmeasure.org",
    code: "mL"
  },
  performer: [
    {
      function: {
        coding: [
          {
            system: "http://terminology.hl7.org/CodeSystem/v2-0443",
            code: "AP",
            display: "Administering Provider"
          }
        ]
      },
      actor: {
        reference: practitionerReference,
        display: "Practitioner"
      }
    }
  ],
  note: immunization.notes ? [
    {
      text: immunization.notes,
      time: new Date().toISOString()
    }
  ] : undefined,
  reasonCode: immunization.indication ? [
    {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: immunization.indicationCode || "169443000",
          display: immunization.indication || "Prevention of disease"
        }
      ]
    }
  ] : undefined,
  protocolApplied: [
    {
      series: immunization.series || "1",
      doseNumberPositiveInt: immunization.doseNumber || 1,
      seriesDosesPositiveInt: immunization.totalDoses || 1
    }
  ]
});

/**
 * Creates ABDM-compliant Composition resource for ImmunizationRecord
 * @param {string} patientReference - Patient resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @param {string} organizationReference - Organization resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {Array} immunizationReferences - Array of Immunization resource references
 * @param {Array} documentReferences - Array of DocumentReference resource references
 * @returns {Object} FHIR Composition resource
 */
export const createABDMImmunizationCompositionResource = (
  patientReference,
  practitionerReference,
  organizationReference,
  encounterReference,
  immunizationReferences,
  documentReferences
) => ({
  resourceType: "Composition",
  id: `composition-immunization-${Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ImmunizationRecord"]
  },
  identifier: {
    system: "https://www.healtether.com/immunization-composition",
    value: `immunization-comp-${Date.now()}`
  },
  status: "final",
  type: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: "41000179103",
        display: "Immunization record"
      }
    ]
  },
  category: [
    {
      coding: [
        {
          system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-record-type",
          code: "immunization",
          display: "Immunization"
        }
      ]
    }
  ],
  subject: {
    reference: patientReference,
    display: "Patient"
  },
  encounter: {
    reference: encounterReference,
    display: "Encounter"
  },
  date: new Date().toISOString(),
  author: [
    {
      reference: practitionerReference,
      display: "Practitioner"
    }
  ],
  title: "Immunization Record",
  custodian: {
    reference: organizationReference,
    display: "Organization"
  },
  section: [
    {
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "41000179103",
            display: "Immunization record"
          }
        ]
      },
      entry: [
        ...immunizationReferences.map(ref => ({
          reference: ref,
          display: "Immunization"
        })),
        ...documentReferences.map(ref => ({
          reference: ref,
          display: "Document Reference"
        }))
      ]
    }
  ]
});

/**
 * Creates ABDM-compliant ImmunizationRecord structure for NDHM FHIR R4 compliance
 * @param {string} artifact - Artifact type (e.g., "ImmunizationRecord")
 * @param {Object} clinicData - Clinic/organization data
 * @param {Object} appointmentData - Appointment/encounter data
 * @param {Object} patient - Patient data with ABHA information
 * @param {Object} practitionerData - Practitioner/doctor data
 * @param {Array} immunizationPrescriptions - Array of immunization prescriptions
 * @returns {Object} ABDM-compliant ImmunizationRecord structure
 */
export const createABDMCompliantImmunizationStructure = async (
  artifact,
  clinicData,
  appointmentData,
  patient,
  practitionerData,
  immunizationPrescriptions
) => {
  try {
    // Create resource references following FHIR standards
    const patientReference = `Patient/${patient._id}`;
    const practitionerReference = `Practitioner/${practitionerData._id}`;
    const organizationReference = `Organization/${clinicData._id}`;
    const encounterReference = `Encounter/${appointmentData._id}`;

    // Create ABDM-compliant Immunization resources
    const immunizationResources = immunizationPrescriptions?.map(immunization =>
      createABDMImmunizationResource(
        immunization,
        patientReference,
        encounterReference,
        practitionerReference
      )
    ) || [];

    // Process vaccination certificates
    const vaccineCertificates = appointmentData.vaccineCertificate || [];
    const documentReferencePromises = vaccineCertificates.length > 0
      ? vaccineCertificates
          .filter((item) => item.blobName)
          .map((item) => createDocumentReference(item.blobName, clinicData._id))
      : [];

    const documentReferences = await Promise.all(documentReferencePromises);

    // Create resource reference arrays
    const immunizationReferences = immunizationResources.map(resource => `Immunization/${resource.id}`);
    const documentReferenceIds = documentReferences.map((doc, index) => `DocumentReference/vaccine-cert-${index}`);

    // Create ABDM-compliant Composition resource
    const compositionResource = createABDMImmunizationCompositionResource(
      patientReference,
      practitionerReference,
      organizationReference,
      encounterReference,
      immunizationReferences,
      documentReferenceIds
    );

    // Return ABDM-compliant structure for Communications service
    return {
      // General NDHM metadata
      general: createGeneralDetails(
        artifact,
        "https://www.healtether.com",
        ["hip1", "hip2"],
        "final",
        "SBX_003515"
      ),

      // Core healthcare entities
      patient: createPatientDetails(
        patient._id,
        patient.abhaNumber,
        patient.abhaAddress,
        {
          text: `${patient.firstName + patient.lastName}`,
          prefix: [`${patient.prefix}`],
        },
        patient.gender,
        patient.birthday,
        patient?.address,
        [`${practitionerData.firstName + practitionerData.lastName}`],
        [],
        patient.mobile
      ),
      practitioners: [createPractitionerDetails(practitionerData)],
      organization: createOrganizationDetails(clinicData),
      encounter: createEncounterDetails(appointmentData),

      // ABDM-compliant FHIR resources
      composition: compositionResource,
      immunizations: immunizationResources,
      documentReferences: documentReferences,

      // Digital signature for authenticity
      signature: createSignatureDetails("", [
        `${practitionerData.firstName + practitionerData.lastName}`,
      ]),
    };
  } catch (error) {
    console.error("Error creating ABDM-compliant Immunization structure:", error);
    throw new Error(`ABDM Immunization Creation Failed: ${error.message}`);
  }
};