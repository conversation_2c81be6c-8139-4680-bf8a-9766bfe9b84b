 
import {
  createGeneralDetails,
  createPatientDetails,
  createPractitionerDetails,
  createOrganizationDetails,
  createEncounterDetails,
  createBinaryDetails,
  createSignatureDetails,
  createDocumentReference,
  mapVitalsToObservations
} from './fhir.data.js';

/**
 * Creates ABDM-compliant Condition resource
 * @param {Object} condition - Condition/diagnosis details
 * @param {string} patientReference - Patient resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @returns {Object} FHIR Condition resource
 */
export const createABDMConditionResource = (
  condition,
  patientReference,
  encounterReference,
  practitionerReference
) => ({
  resourceType: "Condition",
  id: `condition-${condition._id || Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Condition"]
  },
  identifier: [
    {
      use: "usual",
      system: "https://www.healtether.com/condition",
      value: condition._id?.toString() || `condition-${Date.now()}`
    }
  ],
  clinicalStatus: {
    coding: [
      {
        system: "http://terminology.hl7.org/CodeSystem/condition-clinical",
        code: "active",
        display: "Active"
      }
    ]
  },
  verificationStatus: {
    coding: [
      {
        system: "http://terminology.hl7.org/CodeSystem/condition-ver-status",
        code: "confirmed",
        display: "Confirmed"
      }
    ]
  },
  category: [
    {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/condition-category",
          code: "encounter-diagnosis",
          display: "Encounter Diagnosis"
        }
      ]
    }
  ],
  code: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: condition.snomedCode || "404684003",
        display: condition.name || "Clinical finding"
      }
    ],
    text: condition.name || "Clinical finding"
  },
  subject: {
    reference: patientReference,
    display: "Patient"
  },
  encounter: {
    reference: encounterReference,
    display: "Encounter"
  },
  onsetDateTime: condition.onsetDate || new Date().toISOString(),
  recordedDate: new Date().toISOString(),
  recorder: {
    reference: practitionerReference,
    display: "Practitioner"
  },
  asserter: {
    reference: practitionerReference,
    display: "Practitioner"
  },
  note: condition.notes ? [
    {
      text: condition.notes,
      time: new Date().toISOString()
    }
  ] : undefined
});

/**
 * Creates ABDM-compliant MedicationRequest resource
 * @param {Object} medication - Medication prescription details
 * @param {string} patientReference - Patient resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @returns {Object} FHIR MedicationRequest resource
 */
export const createABDMMedicationRequestResource = (
  medication,
  patientReference,
  encounterReference,
  practitionerReference
) => ({
  resourceType: "MedicationRequest",
  id: `medication-request-${medication._id || Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest"]
  },
  identifier: [
    {
      use: "usual",
      system: "https://www.healtether.com/medication-request",
      value: medication._id?.toString() || `med-req-${Date.now()}`
    }
  ],
  status: "active",
  intent: "order",
  category: [
    {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/medicationrequest-category",
          code: "outpatient",
          display: "Outpatient"
        }
      ]
    }
  ],
  medicationCodeableConcept: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: medication.snomedCode || "387517004",
        display: medication.drugName || "Medication"
      }
    ],
    text: medication.drugName || "Medication"
  },
  subject: {
    reference: patientReference,
    display: "Patient"
  },
  encounter: {
    reference: encounterReference,
    display: "Encounter"
  },
  authoredOn: new Date().toISOString(),
  requester: {
    reference: practitionerReference,
    display: "Practitioner"
  },
  dosageInstruction: [
    {
      text: medication.notes || medication.content || "As directed",
      timing: {
        repeat: {
          frequency: medication.frequency || 1,
          period: medication.duration?.value || 1,
          periodUnit: medication.duration?.unit || "d"
        }
      },
      route: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "26643006",
            display: "Oral route"
          }
        ]
      },
      doseAndRate: [
        {
          doseQuantity: {
            value: medication.dosage || 1,
            unit: "tablet",
            system: "http://unitsofmeasure.org",
            code: "1"
          }
        }
      ],
      additionalInstruction: medication.content ? [
        {
          text: medication.content
        }
      ] : undefined
    }
  ],
  dispenseRequest: {
    quantity: {
      value: medication.quantity || 10,
      unit: "tablet",
      system: "http://unitsofmeasure.org",
      code: "1"
    }
  }
});

/**
 * Creates ABDM-compliant ServiceRequest resource for lab tests
 * @param {Object} labTest - Lab test details
 * @param {string} patientReference - Patient resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @returns {Object} FHIR ServiceRequest resource
 */
export const createABDMServiceRequestResource = (
  labTest,
  patientReference,
  encounterReference,
  practitionerReference
) => ({
  resourceType: "ServiceRequest",
  id: `service-request-${labTest._id || Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ServiceRequest"]
  },
  identifier: [
    {
      use: "usual",
      system: "https://www.healtether.com/service-request",
      value: labTest._id?.toString() || `service-req-${Date.now()}`
    }
  ],
  status: "active",
  intent: "order",
  category: [
    {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "108252007",
          display: "Laboratory procedure"
        }
      ]
    }
  ],
  code: {
    coding: [
      {
        system: "http://loinc.org",
        code: labTest.loincCode || "33747-0",
        display: labTest.name || "Laboratory test"
      }
    ],
    text: labTest.name || "Laboratory test"
  },
  subject: {
    reference: patientReference,
    display: "Patient"
  },
  encounter: {
    reference: encounterReference,
    display: "Encounter"
  },
  occurrenceDateTime: new Date().toISOString(),
  requester: {
    reference: practitionerReference,
    display: "Practitioner"
  },
  reasonCode: labTest.reason ? [
    {
      text: labTest.reason
    }
  ] : undefined,
  note: labTest.notes ? [
    {
      text: labTest.notes,
      time: new Date().toISOString()
    }
  ] : undefined
});

/**
 * Creates ABDM-compliant Composition resource for OPConsultRecord following NDHM specification
 * @param {string} patientReference - Patient resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @param {string} organizationReference - Organization resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {Array} conditionReferences - Array of Condition resource references
 * @param {Array} medicationReferences - Array of MedicationRequest resource references
 * @param {Array} serviceRequestReferences - Array of ServiceRequest resource references
 * @param {Array} observationReferences - Array of Observation resource references
 * @param {Array} allergyReferences - Array of AllergyIntolerance resource references
 * @param {Array} procedureReferences - Array of Procedure resource references
 * @param {Array} familyHistoryReferences - Array of FamilyMemberHistory resource references
 * @param {Array} documentReferences - Array of DocumentReference resource references
 * @returns {Object} FHIR Composition resource compliant with ABDM OPConsultRecord
 */
export const createABDMOPConsultCompositionResource = (
  patientReference,
  practitionerReference,
  organizationReference,
  encounterReference,
  conditionReferences,
  medicationReferences,
  serviceRequestReferences,
  observationReferences,
  allergyReferences,
  procedureReferences,
  familyHistoryReferences,
  documentReferences
) => ({
  resourceType: "Composition",
  id: `composition-opconsult-${Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/OPConsultRecord"]
  },
  identifier: {
    system: "https://www.healtether.com/opconsult-composition",
    value: `opconsult-comp-${Date.now()}`
  },
  status: "final",
  // ABDM Required: Fixed SNOMED code for OPConsultRecord
  type: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: "371530004",
        display: "Clinical consultation report"
      }
    ],
    text: "Clinical consultation report"
  },
  subject: {
    reference: patientReference
  },
  encounter: {
    reference: encounterReference
  },
  date: new Date().toISOString(),
  author: [
    {
      reference: practitionerReference
    }
  ],
  title: "OP Consult Record",
  custodian: {
    reference: organizationReference
  },
  // ABDM Required: Specific sections with fixed SNOMED codes
  section: [
    // 1. Chief Complaints Section - ABDM Required
    {
      title: "Chief Complaints",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "422843007",
            display: "Chief complaint section"
          }
        ]
      },
      entry: conditionReferences.map(ref => ({
        reference: ref
      }))
    },
    // 2. Physical Examination Section - ABDM Required
    {
      title: "Physical Examination",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "425044008",
            display: "Physical exam section"
          }
        ]
      },
      entry: observationReferences.map(ref => ({
        reference: ref
      }))
    },
    // 3. Allergies Section - ABDM Required
    {
      title: "Allergies",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "722446000",
            display: "Allergy record"
          }
        ]
      },
      entry: allergyReferences.map(ref => ({
        reference: ref
      }))
    },
    // 4. Medical History Section - ABDM Required
    {
      title: "Medical History",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "371529009",
            display: "History and physical report"
          }
        ]
      },
      entry: conditionReferences.map(ref => ({
        reference: ref
      }))
    },
    // 5. Family History Section - ABDM Required
    {
      title: "Family History",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "422432008",
            display: "Family history section"
          }
        ]
      },
      entry: familyHistoryReferences.map(ref => ({
        reference: ref
      }))
    },
    // 6. Investigation Advice Section - ABDM Required
    {
      title: "Investigation Advice",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "721963009",
            display: "Order document"
          }
        ]
      },
      entry: serviceRequestReferences.map(ref => ({
        reference: ref
      }))
    },
    // 7. Medications Section - ABDM Required
    {
      title: "Medications",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "422979000",
            display: "Medication section"
          }
        ]
      },
      entry: medicationReferences.map(ref => ({
        reference: ref
      }))
    },
    // 8. Follow Up Section - ABDM Required
    {
      title: "Follow Up",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "736271009",
            display: "Outpatient care plan"
          }
        ]
      },
      entry: [] // Can be populated with CarePlan resources
    },
    // 9. Procedure Section - ABDM Required
    {
      title: "Procedure",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "371525003",
            display: "Clinical procedure report"
          }
        ]
      },
      entry: procedureReferences.map(ref => ({
        reference: ref
      }))
    },
    // 10. Document Reference Section - ABDM Required
    {
      title: "Document Reference",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "371530004",
            display: "Clinical consultation report"
          }
        ]
      },
      entry: documentReferences.map(ref => ({
        reference: ref
      }))
    }
  ]
});

/**
 * Creates ABDM-compliant DocumentBundle for OPConsult records
 * @param {Object} compositionResource - FHIR Composition resource
 * @param {Object} patientResource - FHIR Patient resource
 * @param {Object} practitionerResource - FHIR Practitioner resource
 * @param {Object} organizationResource - FHIR Organization resource
 * @param {Object} encounterResource - FHIR Encounter resource
 * @param {Array} conditionResources - Array of FHIR Condition resources
 * @param {Array} medicationResources - Array of FHIR MedicationRequest resources
 * @param {Array} serviceRequestResources - Array of FHIR ServiceRequest resources
 * @param {Array} observationResources - Array of FHIR Observation resources
 * @param {Array} documentReferences - Array of FHIR DocumentReference resources
 * @returns {Object} ABDM-compliant DocumentBundle
 */
export const createABDMOPConsultDocumentBundle = (
  compositionResource,
  patientResource,
  practitionerResource,
  organizationResource,
  encounterResource,
  conditionResources,
  medicationResources,
  serviceRequestResources,
  observationResources,
  documentReferences
) => ({
  resourceType: "Bundle",
  id: `opconsult-bundle-${Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle"]
  },
  identifier: {
    system: "https://www.healtether.com/bundle",
    value: `opconsult-bundle-${Date.now()}`
  },
  type: "document",
  timestamp: new Date().toISOString(),
  entry: [
    // 1. Composition (must be first entry in document bundle)
    {
      fullUrl: `Composition/${compositionResource.id}`,
      resource: compositionResource
    },
    // 2. Patient
    {
      fullUrl: `Patient/${patientResource.id}`,
      resource: patientResource
    },
    // 3. Practitioner
    {
      fullUrl: `Practitioner/${practitionerResource.id}`,
      resource: practitionerResource
    },
    // 4. Organization
    {
      fullUrl: `Organization/${organizationResource.id}`,
      resource: organizationResource
    },
    // 5. Encounter
    {
      fullUrl: `Encounter/${encounterResource.id}`,
      resource: encounterResource
    },
    // 6. Conditions
    ...conditionResources.map(condition => ({
      fullUrl: `Condition/${condition.id}`,
      resource: condition
    })),
    // 7. MedicationRequests
    ...medicationResources.map(medication => ({
      fullUrl: `MedicationRequest/${medication.id}`,
      resource: medication
    })),
    // 8. ServiceRequests
    ...serviceRequestResources.map(serviceRequest => ({
      fullUrl: `ServiceRequest/${serviceRequest.id}`,
      resource: serviceRequest
    })),
    // 9. Observations
    ...observationResources.map(observation => ({
      fullUrl: `Observation/${observation.id}`,
      resource: observation
    })),
    // 10. DocumentReferences
    ...documentReferences.map(docRef => ({
      fullUrl: `DocumentReference/${docRef.id}`,
      resource: docRef
    }))
  ]
});

/**
 * Creates ABDM-compliant OPConsultRecord structure for NDHM FHIR R4 compliance
 * @param {string} artifact - Artifact type (e.g., "OPConsultRecord")
 * @param {Object} clinicData - Clinic/organization data
 * @param {Object} patient - Patient data with ABHA information
 * @param {Object} appointmentData - Appointment/encounter data
 * @param {Object} practitionerData - Practitioner/doctor data
 * @param {Object} prescription - Prescription data with medications, diagnosis, etc.
 * @param {Object} medicalHistory - Patient medical history
 * @returns {Object} ABDM-compliant OPConsultRecord structure with DocumentBundle
 */
export const createABDMCompliantOPConsultStructure = async (
  artifact,
  clinicData,
  patient,
  appointmentData,
  practitionerData,
  prescription,
  medicalHistory
) => {
  try {
    // Create resource references following FHIR standards
    const patientReference = `Patient/${patient._id}`;
    const practitionerReference = `Practitioner/${practitionerData._id}`;
    const organizationReference = `Organization/${clinicData._id}`;
    const encounterReference = `Encounter/${appointmentData._id}`;

    // Create FHIR resources
    const patientResource = createPatientDetails(
      patient._id,
      patient.abhaNumber,
      patient.abhaAddress,
      {
        text: `${patient.firstName + patient.lastName}`,
        prefix: [`${patient.prefix}`],
      },
      patient.gender,
      patient.birthday,
      patient?.address,
      [`${practitionerData.firstName + practitionerData.lastName}`],
      medicalHistory.allergies || [],
      patient.mobile
    );

    const practitionerResource = createPractitionerDetails(practitionerData, patient._id);
    const organizationResource = createOrganizationDetails(clinicData);
    const encounterResource = createEncounterDetails(appointmentData);

    // Create ABDM-compliant clinical resources
    const conditionResources = prescription.prescriptions?.diagnosis?.map(diagnosis =>
      createABDMConditionResource(
        diagnosis,
        patientReference,
        encounterReference,
        practitionerReference
      )
    ) || [];

    const medicationResources = prescription.prescriptions?.drugPrescriptions?.map(medication =>
      createABDMMedicationRequestResource(
        medication,
        patientReference,
        encounterReference,
        practitionerReference
      )
    ) || [];

    const serviceRequestResources = prescription.prescriptions?.labTests?.map(labTest =>
      createABDMServiceRequestResource(
        labTest,
        patientReference,
        encounterReference,
        practitionerReference
      )
    ) || [];

    // Process document references
    const allDocuments = [
      ...(appointmentData.medicalRecords || []),
      ...(appointmentData.procedureRecords || []),
      ...(appointmentData.prescriptionRecords || [])
    ];

    const documentReferencePromises = allDocuments.length > 0
      ? allDocuments
          .filter((item) => item.blobName)
          .map((item) => createDocumentReference(item.blobName, clinicData._id))
      : [];

    const documentReferences = await Promise.all(documentReferencePromises);
    const observationResources = mapVitalsToObservations(prescription.vitals || {});

    // Create resource reference arrays
    const conditionReferences = conditionResources.map(resource => `Condition/${resource.id}`);
    const medicationReferences = medicationResources.map(resource => `MedicationRequest/${resource.id}`);
    const serviceRequestReferences = serviceRequestResources.map(resource => `ServiceRequest/${resource.id}`);
    const observationReferences = observationResources.map((_, index) => `Observation/obs-${index}`);
    const documentReferenceIds = documentReferences.map((_, index) => `DocumentReference/doc-${index}`);

    // Create additional ABDM-required resources
    const allergyReferences = []; // TODO: Create AllergyIntolerance resources from medical history
    const procedureReferences = []; // TODO: Create Procedure resources if any procedures performed
    const familyHistoryReferences = []; // TODO: Create FamilyMemberHistory resources

    // Create ABDM-compliant Composition resource
    const compositionResource = createABDMOPConsultCompositionResource(
      patientReference,
      practitionerReference,
      organizationReference,
      encounterReference,
      conditionReferences,
      medicationReferences,
      serviceRequestReferences,
      observationReferences,
      allergyReferences,
      procedureReferences,
      familyHistoryReferences,
      documentReferenceIds
    );

    // Create ABDM DocumentBundle
    const documentBundle = createABDMOPConsultDocumentBundle(
      compositionResource,
      patientResource,
      practitionerResource,
      organizationResource,
      encounterResource,
      conditionResources,
      medicationResources,
      serviceRequestResources,
      observationResources,
      documentReferences
    );

    // Return ABDM-compliant structure for Communications service
    return {
      // ABDM DocumentBundle (primary structure)
      bundle: documentBundle,

      // Legacy structure for backward compatibility
      general: createGeneralDetails(
        artifact,
        "https://www.healtether.com",
        ["hip1", "hip2"],
        "final",
        "SBX_003515"
      ),
      patient: patientResource,
      practitioners: [practitionerResource],
      organization: organizationResource,
      encounter: encounterResource,
      composition: compositionResource,
      conditions: conditionResources,
      medicationRequests: medicationResources,
      serviceRequests: serviceRequestResources,
      observations: observationResources,
      documentReferences: documentReferences,
      signature: createSignatureDetails("", [
        `${practitionerData.firstName + practitionerData.lastName}`,
      ]),
    };
  } catch (error) {
    console.error("Error creating ABDM-compliant OPConsult structure:", error);
    throw new Error(`ABDM OPConsult Creation Failed: ${error.message}`);
  }
};
