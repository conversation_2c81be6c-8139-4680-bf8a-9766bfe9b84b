import { Schema } from 'mongoose';

const telecomSchema = new Schema({
    system: String,
    value: String,
    use: String
});

const licenseSchema = new Schema({
    code: String,
    display: String,
    licNo: String
});

// ABDM-compliant coding schema
const codingSchema = new Schema(
  {
    system: { type: String },
    code: { type: String },
    display: { type: String }
  },
  { _id: false }
);

// ABDM-compliant CodeableConcept schema
const codeableConceptSchema = new Schema(
  {
    coding: [codingSchema],
    text: { type: String }
  },
  { _id: false }
);

// ABDM-compliant Reference schema
const referenceSchema = new Schema(
  {
    reference: { type: String },
    display: { type: String }
  },
  { _id: false }
);

// ABDM-compliant Quantity schema
const quantitySchema = new Schema(
  {
    value: { type: Number },
    unit: { type: String },
    system: { type: String },
    code: { type: String }
  },
  { _id: false }
);

const signatureSchema = new Schema({
    who: {
        type: { type: String },
        name: String
    },
    sigFormat: String,
    data: String
});
const addressSchema = new Schema({
    use: String,
    type: String,
    text: String,
   // line: [String],
    city: String,
    state: String,
    district: String,
    postalCode: String,
    country: String
});

const patientSchema = new Schema({
    id:String,
    abhaNumber: String,
    abhaAddress: String,
    name: {
        text:String,
        prefix:[String]
    },
    gender: String,
    dob: String,
    doctors: [String],
    telecom: [telecomSchema],
    address:[addressSchema]
});

const generalSchema = new Schema({
    artifact: String,
    hipUrl: String,
    hipIds: [String],
    status: String,
    clientId: String
});

const practitionerSchema = new Schema({
    names: [String],
    licenses: [licenseSchema],
    patient: String,
    gender: String,
    birthDate: String,
    address: [addressSchema],
    telecom: [telecomSchema],
});

const encounterSchema = new Schema({
    status: String,
    startTime: String,
    endTime: String
});

const organizationSchema = new Schema({
    name: String,
    telecom: [telecomSchema],
    licenses: [licenseSchema]
});
// ABDM-compliant Immunization schema
const immunizationSchema = new Schema({
    resourceType: { type: String, default: "Immunization" },
    id: String,
    meta: {
        versionId: String,
        lastUpdated: String,
        profile: [String]
    },
    identifier: [{
        use: String,
        system: String,
        value: String
    }],
    status: String,
    statusReason: codeableConceptSchema,
    vaccineCode: codeableConceptSchema,
    patient: referenceSchema,
    encounter: referenceSchema,
    occurrenceDateTime: String,
    occurrenceString: String,
    recorded: String,
    primarySource: Boolean,
    reportOrigin: codeableConceptSchema,
    location: referenceSchema,
    manufacturer: referenceSchema,
    lotNumber: String,
    expirationDate: String,
    site: codeableConceptSchema,
    route: codeableConceptSchema,
    doseQuantity: quantitySchema,
    performer: [{
        function: codeableConceptSchema,
        actor: referenceSchema
    }],
    note: [{
        authorString: String,
        time: String,
        text: String
    }],
    reasonCode: [codeableConceptSchema],
    reasonReference: [referenceSchema],
    isSubpotent: Boolean,
    subpotentReason: [codeableConceptSchema],
    education: [{
        documentType: String,
        reference: String,
        publicationDate: String,
        presentationDate: String
    }],
    programEligibility: [codeableConceptSchema],
    fundingSource: codeableConceptSchema,
    reaction: [{
        date: String,
        detail: referenceSchema,
        reported: Boolean
    }],
    protocolApplied: [{
        series: String,
        authority: referenceSchema,
        targetDisease: [codeableConceptSchema],
        doseNumberPositiveInt: Number,
        doseNumberString: String,
        seriesDosesPositiveInt: Number,
        seriesDosesString: String
    }]
});

const attachmentSchema = new Schema({
    contentType: String,
    language: String,
    data: String,
    title: String,
    creation: String
});

const documentReferenceSchema = new Schema({
    status: String,
    docStatus: String,
    type: String,
    content: [{ attachment: attachmentSchema }]
});

const ImmunizationRecordSchema = new Schema({
    fhirId: {
        type: String,
        required: true,
        index: true
    },
    general: generalSchema,
    patient: patientSchema,
    practitioners: [practitionerSchema],
    encounter: encounterSchema,
    organization: organizationSchema,
    immunizations:[immunizationSchema],
     documentReferences: [documentReferenceSchema],
    signature: signatureSchema,
    abhaCareContextLinked:{
        type: Boolean,
        default: false
    }
});



export { ImmunizationRecordSchema };
