import { v4 as uuidv4 } from "uuid";

/**
 * Creates ABDM-compliant ChargeItem resource following NDHM FHIR R4 standards
 * @param {Object} treatment - Treatment/service data
 * @param {string} patientReference - Patient resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @returns {Object} FHIR ChargeItem resource compliant with ABDM standards
 */
export const generateABDMChargeItemResource = (
  treatment,
  patientReference,
  practitionerReference,
  encounterReference
) => ({
  fullUrl: `urn:uuid:${uuidv4()}`,
  resource: {
    resourceType: "ChargeItem",
    id: uuidv4(),
    meta: {
      versionId: "1",
      lastUpdated: new Date().toISOString(),
      profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ChargeItem"]
    },
    status: "billed",
    code: {
      coding: [
        {
          system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-billing-codes",
          code: "00", // Consultation services
          display: "Consultation"
        }
      ]
    },
    subject: {
      reference: patientReference,
      display: "Patient"
    },
    context: {
      reference: encounterReference,
      display: "Encounter"
    },
    occurrenceDateTime: new Date().toISOString(),
    performer: [
      {
        function: {
          coding: [
            {
              system: "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
              code: "PRF",
              display: "Performer"
            }
          ]
        },
        actor: {
          reference: practitionerReference,
          display: "Practitioner"
        }
      }
    ],
    quantity: {
      value: treatment.quantity || 1,
      unit: treatment.unit || "service",
      system: "http://unitsofmeasure.org",
      code: "1"
    },
    priceOverride: {
      value: parseFloat(treatment.amount?.toString() || "0"),
      currency: "INR"
    },
    overrideReason: treatment.discRate > 0 ? "Discount applied" : undefined,
    enterer: {
      reference: practitionerReference,
      display: "Practitioner"
    },
    enteredDate: new Date().toISOString()
  }
});

/**
 * Creates ABDM-compliant Invoice resource following NDHM FHIR R4 standards
 * @param {Object} invoice - Invoice data
 * @param {Object} patientResource - Patient resource
 * @param {Array} practitionerResources - Practitioner resources
 * @param {Object} organizationResource - Organization resource
 * @param {Array} chargeItemResources - ChargeItem resources
 * @param {string} currentTime - Current timestamp
 * @returns {Object} FHIR Invoice resource compliant with ABDM standards
 */
export const generateABDMInvoiceResource = (
  invoice,
  patientResource,
  practitionerResources,
  organizationResource,
  chargeItemResources,
  currentTime
) => {
  // Calculate totals according to ABDM standards
  const baseAmount = invoice.treatments?.reduce((sum, treatment) =>
    sum + parseFloat(treatment.amount?.toString() || "0"), 0) || 0;
  const totalDiscount = invoice.treatments?.reduce((sum, treatment) =>
    sum + parseFloat(((treatment.amount * (treatment.discRate || 0)) / 100).toString()), 0) || 0;
  const totalCGST = invoice.treatments?.reduce((sum, treatment) =>
    sum + parseFloat(treatment.cgstAmount?.toString() || "0"), 0) || 0;
  const totalSGST = invoice.treatments?.reduce((sum, treatment) =>
    sum + parseFloat(treatment.sgstAmount?.toString() || "0"), 0) || 0;

  // ABDM Standard: totalGross = amount before taxes, totalNet = amount after taxes
  const calculatedTotalGross = baseAmount - totalDiscount;
  const calculatedTotalNet = calculatedTotalGross + totalCGST + totalSGST;

  return {
    fullUrl: `urn:uuid:${uuidv4()}`,
    resource: {
      resourceType: "Invoice",
      id: uuidv4(),
      meta: {
        versionId: "1",
        lastUpdated: currentTime,
        profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Invoice"]
      },
      identifier: [
        {
          value: invoice.invoiceNumber?.toString() || `INV-${Date.now()}`
        }
      ],
      status: "issued",
      type: {
        coding: [
          {
            system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-billing-codes",
            code: "00", // Consultation billing code per ABDM standards
            display: "Consultation"
          }
        ]
      },
      subject: {
        reference: `urn:uuid:${patientResource.resource.id}`
      },
      date: currentTime,
      participant: [
        {
          actor: {
            reference: `urn:uuid:${practitionerResources[0]?.resource.id}`
          }
        }
      ],
      lineItem: invoice.treatments?.map((treatment, index) => ({
        sequence: index + 1,
        chargeItemReference: {
          reference: `urn:uuid:${chargeItemResources[index]?.resource.id}`
        },
        priceComponent: [
          // 1. MRP (informational) - ABDM code "00"
          {
            type: "informational",
            code: {
              coding: [
                {
                  system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
                  code: "00",
                  display: "MRP"
                }
              ]
            },
            amount: {
              value: parseFloat(treatment.mrp?.toString() || treatment.amount?.toString() || "0"),
              currency: "INR"
            }
          },
          // 2. Rate (base price) - ABDM code "01"
          {
            type: "base",
            code: {
              coding: [
                {
                  system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
                  code: "01",
                  display: "Rate"
                }
              ]
            },
            amount: {
              value: parseFloat(treatment.amount?.toString() || "0"),
              currency: "INR"
            }
          },
          // 3. Discount - ABDM code "02"
          {
            type: "discount",
            code: {
              coding: [
                {
                  system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
                  code: "02",
                  display: "Discount"
                }
              ]
            },
            amount: {
              value: parseFloat(((treatment.amount * (treatment.discRate || 0)) / 100).toString()),
              currency: "INR"
            }
          },
          // 4. CGST (tax) - ABDM code "03"
          {
            type: "tax",
            code: {
              coding: [
                {
                  system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
                  code: "03",
                  display: "CGST"
                }
              ]
            },
            amount: {
              value: parseFloat(treatment.cgstAmount?.toString() || "0"),
              currency: "INR"
            }
          },
          // 5. SGST (tax) - ABDM code "04"
          {
            type: "tax",
            code: {
              coding: [
                {
                  system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
                  code: "04",
                  display: "SGST"
                }
              ]
            },
            amount: {
              value: parseFloat(treatment.sgstAmount?.toString() || "0"),
              currency: "INR"
            }
          }
        ]
      })) || [],
      // ABDM Standard: totalGross = before taxes, totalNet = after taxes
      totalNet: {
        value: calculatedTotalNet,
        currency: "INR"
      },
      totalGross: {
        value: calculatedTotalGross,
        currency: "INR"
      }
    }
  };
};
